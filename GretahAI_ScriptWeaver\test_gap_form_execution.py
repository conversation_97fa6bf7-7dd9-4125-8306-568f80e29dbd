#!/usr/bin/env python3
"""
Direct test of display_gap_filling_form() execution flow to identify missing debug log entries.

This script bypasses the AI API entirely and directly tests the gap filling form
with various scenarios to determine why specific debug log entries are not appearing.
"""

import sys
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Mock Streamlit before importing
class MockSessionState:
    def __init__(self):
        self._data = {}

    def __getattr__(self, name):
        return self._data.get(name, None)

    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            if not hasattr(self, '_data'):
                super().__setattr__('_data', {})
            self._data[name] = value

    def __getitem__(self, key):
        return self._data.get(key)

    def __setitem__(self, key, value):
        self._data[key] = value

    def __contains__(self, key):
        return key in self._data

    def get(self, key, default=None):
        return self._data.get(key, default)

    def keys(self):
        return self._data.keys()

    def clear(self):
        self._data.clear()

    def update(self, other):
        self._data.update(other)

class MockStreamlit:
    def __init__(self):
        self.session_state = MockSessionState()
        self._columns_called = False
        self._form_called = False
        self._button_called = False
        
    def markdown(self, text):
        print(f"ST.MARKDOWN: {text}")
        
    def info(self, text):
        print(f"ST.INFO: {text}")
        
    def warning(self, text):
        print(f"ST.WARNING: {text}")
        
    def error(self, text):
        print(f"ST.ERROR: {text}")
        
    def success(self, text):
        print(f"ST.SUCCESS: {text}")
        
    def columns(self, num):
        print(f"ST.COLUMNS: Creating {num} columns")
        self._columns_called = True
        return [MockColumn() for _ in range(num)]
        
    def button(self, label, **kwargs):
        print(f"ST.BUTTON: {label} (key: {kwargs.get('key', 'no_key')})")
        self._button_called = True
        return False  # Never clicked in test
        
    def form(self, key):
        print(f"ST.FORM: Creating form with key: {key}")
        self._form_called = True
        return MockForm()
        
    def stop(self):
        print("ST.STOP: Called")
        
    def spinner(self, text):
        print(f"ST.SPINNER: {text}")
        return MockSpinner()

class MockColumn:
    def __enter__(self):
        print("COLUMN: Entering column context")
        return self
        
    def __exit__(self, *args):
        print("COLUMN: Exiting column context")

class MockForm:
    def __enter__(self):
        print("FORM: Entering form context")
        return self
        
    def __exit__(self, *args):
        print("FORM: Exiting form context")

class MockSpinner:
    def __enter__(self):
        return self
        
    def __exit__(self, *args):
        pass

# Mock streamlit
mock_st = MockStreamlit()
sys.modules['streamlit'] = mock_st

# Now import the gap analysis module
from core.gap_analysis import display_gap_filling_form
from core.stage10_logger import log_gap_analysis, get_stage10_logger

def test_gap_form_execution_scenarios():
    """Test various scenarios for gap form execution."""
    
    print("=== Testing Gap Form Execution Scenarios ===")
    
    # Initialize logger
    logger = get_stage10_logger()
    print(f"Debug mode enabled: {logger.debug_mode}")
    
    # Test scenarios with different gap configurations
    test_scenarios = [
        {
            "name": "Scenario 1: Single locator gap with valid website URL",
            "gaps": [
                {
                    "id": "gap_locator_1",
                    "type": "locator",
                    "description": "Submit button CSS selector",
                    "required": True,
                    "suggested_values": ["#submit", ".submit-btn"]
                }
            ],
            "website_url": "https://example.com",
            "form_key": "test_form_1",
            "expected_path": "interactive_selector_enabled"
        },
        {
            "name": "Scenario 2: Single locator gap with no website URL",
            "gaps": [
                {
                    "id": "gap_locator_2", 
                    "type": "locator",
                    "description": "Login button selector",
                    "required": True
                }
            ],
            "website_url": None,
            "form_key": "test_form_2",
            "expected_path": "interactive_selector_disabled"
        },
        {
            "name": "Scenario 3: Single locator gap with invalid website URL",
            "gaps": [
                {
                    "id": "gap_locator_3",
                    "type": "locator", 
                    "description": "Search input field",
                    "required": True
                }
            ],
            "website_url": "invalid-url-format",
            "form_key": "test_form_3",
            "expected_path": "interactive_selector_disabled"
        },
        {
            "name": "Scenario 4: Multiple locator gaps with valid website URL",
            "gaps": [
                {
                    "id": "gap_locator_4a",
                    "type": "locator",
                    "description": "Username field",
                    "required": True
                },
                {
                    "id": "gap_locator_4b", 
                    "type": "locator",
                    "description": "Password field",
                    "required": True
                }
            ],
            "website_url": "https://test-site.com",
            "form_key": "test_form_4",
            "expected_path": "interactive_selector_enabled"
        },
        {
            "name": "Scenario 5: Mixed gap types with locator gaps",
            "gaps": [
                {
                    "id": "gap_data_5a",
                    "type": "test_data",
                    "description": "Username value",
                    "required": True
                },
                {
                    "id": "gap_locator_5b",
                    "type": "locator",
                    "description": "Submit button",
                    "required": True
                }
            ],
            "website_url": "https://mixed-test.com",
            "form_key": "test_form_5", 
            "expected_path": "interactive_selector_enabled"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\n{'='*60}")
        print(f"Running {scenario['name']}")
        print(f"{'='*60}")
        
        # Reset mock streamlit state
        mock_st.session_state.clear()
        mock_st._columns_called = False
        mock_st._form_called = False
        mock_st._button_called = False
        
        # Log scenario start
        log_gap_analysis("test_scenario_start",
                       scenario_name=scenario['name'],
                       gaps_count=len(scenario['gaps']),
                       website_url=scenario['website_url'],
                       expected_path=scenario['expected_path'])
        
        print(f"Gaps: {len(scenario['gaps'])}")
        print(f"Website URL: {scenario['website_url']}")
        print(f"Form Key: {scenario['form_key']}")
        print(f"Expected Path: {scenario['expected_path']}")
        
        # Call the function directly
        try:
            print(f"\n--- Calling display_gap_filling_form() ---")
            result = display_gap_filling_form(
                gaps=scenario['gaps'],
                form_key=scenario['form_key'],
                website_url=scenario['website_url'],
                avoid_expanders=True
            )
            
            print(f"Function returned: {result}")
            print(f"Streamlit columns called: {mock_st._columns_called}")
            print(f"Streamlit form called: {mock_st._form_called}")
            print(f"Streamlit button called: {mock_st._button_called}")
            
            # Log scenario completion
            log_gap_analysis("test_scenario_complete",
                           scenario_name=scenario['name'],
                           result_type=type(result).__name__,
                           columns_called=mock_st._columns_called,
                           form_called=mock_st._form_called,
                           button_called=mock_st._button_called)
            
        except Exception as e:
            print(f"ERROR: {e}")
            log_gap_analysis("test_scenario_error",
                           scenario_name=scenario['name'],
                           error=str(e))
    
    print(f"\n{'='*60}")
    print("All scenarios completed")
    print(f"{'='*60}")

def test_session_state_conditions():
    """Test various session state conditions that might affect execution."""
    
    print("\n=== Testing Session State Conditions ===")
    
    # Test with various session state flags that might interfere
    test_flags = [
        {},  # Clean state
        {"interactive_selector_in_progress": True},
        {"gap_form_submitted_test_form": True},
        {"interactive_locator_gap_test": "existing_locator"},
        {"stage10_website_url": "https://session-state-url.com"}
    ]
    
    base_gaps = [
        {
            "id": "gap_test",
            "type": "locator", 
            "description": "Test locator gap",
            "required": True
        }
    ]
    
    for i, flags in enumerate(test_flags):
        print(f"\n--- Session State Test {i+1} ---")
        print(f"Session state flags: {flags}")
        
        # Set session state
        mock_st.session_state.clear()
        mock_st.session_state.update(flags)
        
        log_gap_analysis("test_session_state",
                       test_index=i+1,
                       session_flags=list(flags.keys()),
                       flag_count=len(flags))
        
        try:
            result = display_gap_filling_form(
                gaps=base_gaps,
                form_key="session_test_form",
                website_url="https://test.com",
                avoid_expanders=True
            )
            print(f"Result: {result}")
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    print("Gap Form Execution Flow Test")
    print("=" * 50)
    
    # Test gap form execution scenarios
    test_gap_form_execution_scenarios()
    
    # Test session state conditions
    test_session_state_conditions()
    
    print("\nTest completed. Check the console output and log files for detailed execution flow.")
    print("Look for the missing log entries in the debug output above.")
