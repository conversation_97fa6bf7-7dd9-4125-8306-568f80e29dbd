#!/usr/bin/env python3
"""
Test script to verify gap analysis logging functionality.

This script simulates the gap analysis workflow to help diagnose why certain
debug log entries aren't appearing in the Stage 10 gap analysis logs.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import the gap analysis module and logging
from core.gap_analysis import display_gap_filling_form
from core.stage10_logger import log_gap_analysis, get_stage10_logger

def test_gap_analysis_conditions():
    """Test different gap analysis conditions to see which log entries are generated."""
    
    print("=== Testing Gap Analysis Logging Conditions ===")
    
    # Initialize logger
    logger = get_stage10_logger()
    print(f"Debug mode enabled: {logger.debug_mode}")
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "No gaps",
            "gaps": [],
            "website_url": "https://example.com",
            "expected_logs": ["form_no_gaps"]
        },
        {
            "name": "Non-locator gaps only",
            "gaps": [
                {"id": "gap_1", "type": "test_data", "description": "Username", "required": True}
            ],
            "website_url": "https://example.com",
            "expected_logs": ["form_interactive_selector_disabled", "selector_not_needed"]
        },
        {
            "name": "Locator gaps with no website URL",
            "gaps": [
                {"id": "gap_1", "type": "locator", "description": "Submit button selector", "required": True}
            ],
            "website_url": None,
            "expected_logs": ["form_interactive_selector_disabled", "selector_unavailable_no_url"]
        },
        {
            "name": "Locator gaps with invalid website URL",
            "gaps": [
                {"id": "gap_1", "type": "locator", "description": "Submit button selector", "required": True}
            ],
            "website_url": "invalid-url",
            "expected_logs": ["form_interactive_selector_disabled", "selector_unavailable_invalid_url"]
        },
        {
            "name": "Locator gaps with valid website URL",
            "gaps": [
                {"id": "gap_1", "type": "locator", "description": "Submit button selector", "required": True}
            ],
            "website_url": "https://example.com",
            "expected_logs": ["form_interactive_selector_enabled", "selector_button_render"]
        }
    ]
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\n--- Test Scenario {i+1}: {scenario['name']} ---")
        print(f"Gaps: {len(scenario['gaps'])}")
        print(f"Website URL: {scenario['website_url']}")
        print(f"Expected logs: {scenario['expected_logs']}")
        
        # Test the condition logic directly
        gaps = scenario['gaps']
        website_url = scenario['website_url']
        
        # Simulate the main condition evaluation
        locator_gaps = [gap for gap in gaps if gap.get('type') == 'locator']
        main_condition = locator_gaps and website_url and website_url.startswith(('http://', 'https://'))
        
        print(f"Locator gaps found: {len(locator_gaps)}")
        print(f"Website URL valid: {website_url.startswith(('http://', 'https://')) if website_url else False}")
        print(f"Main condition result: {main_condition}")
        
        # Log the condition evaluation
        log_gap_analysis("test_condition_evaluation",
                       scenario_name=scenario['name'],
                       locator_gaps_present=bool(locator_gaps),
                       website_url_present=bool(website_url),
                       website_url_valid=website_url.startswith(('http://', 'https://')) if website_url else False,
                       main_condition_result=main_condition)
        
        # Test specific logging calls that should be triggered
        if not gaps:
            log_gap_analysis("test_form_no_gaps")
        elif not locator_gaps:
            log_gap_analysis("test_selector_not_needed", reason="no_locator_gaps", total_gaps=len(gaps))
        elif main_condition:
            log_gap_analysis("test_form_interactive_selector_enabled", locator_gaps_count=len(locator_gaps))
            for gap in locator_gaps:
                gap_id = gap.get('id', 'unknown')
                description = gap.get('description', 'Unknown gap')
                log_gap_analysis("test_selector_button_render", gap_id=gap_id, description=description[:30])
        else:
            log_gap_analysis("test_form_interactive_selector_disabled",
                           reason="condition_failed",
                           has_locator_gaps=bool(locator_gaps),
                           has_website_url=bool(website_url),
                           website_url_valid=website_url.startswith(('http://', 'https://')) if website_url else False)
            
            if locator_gaps:
                if not website_url:
                    log_gap_analysis("test_selector_unavailable_no_url", locator_gaps_count=len(locator_gaps))
                elif not website_url.startswith(('http://', 'https://')):
                    log_gap_analysis("test_selector_unavailable_invalid_url", website_url=website_url, locator_gaps_count=len(locator_gaps))
    
    print("\n=== Test Complete ===")
    print("Check the debug logs to see which entries were generated.")
    print(f"Log file location: {logger.log_file}")

def test_logging_functions():
    """Test the logging functions directly."""
    print("\n=== Testing Logging Functions Directly ===")
    
    # Test each of the missing log types
    missing_logs = [
        "form_interactive_selector_disabled",
        "selector_button_render", 
        "selector_flags_set"
    ]
    
    for log_type in missing_logs:
        print(f"Testing log type: {log_type}")
        log_gap_analysis(log_type, test_data="direct_function_call", timestamp=str(os.times()))

if __name__ == "__main__":
    print("Gap Analysis Logging Test")
    print("=" * 50)
    
    # Test logging functions
    test_logging_functions()
    
    # Test gap analysis conditions
    test_gap_analysis_conditions()
    
    print("\nTest completed. Check the console output and log files for results.")
