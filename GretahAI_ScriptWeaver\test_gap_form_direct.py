#!/usr/bin/env python3
"""
Direct test of gap analysis execution flow by examining the code logic.

This script analyzes the conditional logic in display_gap_filling_form() to identify
why specific debug log entries are not appearing.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def analyze_gap_form_logic():
    """Analyze the conditional logic in display_gap_filling_form()."""
    
    print("=== Analyzing Gap Form Logic ===")
    
    # Test scenarios that should trigger different code paths
    test_cases = [
        {
            "name": "Case 1: Locator gaps with valid URL",
            "gaps": [{"id": "gap1", "type": "locator", "description": "Submit button"}],
            "website_url": "https://example.com",
            "expected_logs": ["form_interactive_selector_enabled", "selector_button_render"]
        },
        {
            "name": "Case 2: Locator gaps with no URL", 
            "gaps": [{"id": "gap2", "type": "locator", "description": "Login button"}],
            "website_url": None,
            "expected_logs": ["form_interactive_selector_disabled", "selector_unavailable_no_url"]
        },
        {
            "name": "Case 3: Locator gaps with invalid URL",
            "gaps": [{"id": "gap3", "type": "locator", "description": "Search field"}],
            "website_url": "invalid-url",
            "expected_logs": ["form_interactive_selector_disabled", "selector_unavailable_invalid_url"]
        },
        {
            "name": "Case 4: No locator gaps",
            "gaps": [{"id": "gap4", "type": "test_data", "description": "Username"}],
            "website_url": "https://example.com",
            "expected_logs": ["selector_not_needed"]
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        gaps = case['gaps']
        website_url = case['website_url']
        
        # Simulate the logic from display_gap_filling_form()
        locator_gaps = [gap for gap in gaps if gap.get('type') == 'locator']
        
        print(f"Input gaps: {len(gaps)}")
        print(f"Locator gaps found: {len(locator_gaps)}")
        print(f"Website URL: {website_url}")
        
        # Main condition evaluation (line 241 in gap_analysis.py)
        main_condition = locator_gaps and website_url and website_url.startswith(('http://', 'https://'))
        
        print(f"Locator gaps present: {bool(locator_gaps)}")
        print(f"Website URL present: {bool(website_url)}")
        print(f"Website URL valid: {website_url.startswith(('http://', 'https://')) if website_url else False}")
        print(f"Main condition result: {main_condition}")
        
        # Determine execution path
        if main_condition:
            print("EXECUTION PATH: Interactive selector ENABLED")
            print("Expected logs: form_interactive_selector_enabled, selector_button_render")
            
            # Simulate the gap processing loop
            for i, gap in enumerate(locator_gaps):
                gap_id = gap.get('id', f'gap_{i}')
                description = gap.get('description', f'Gap {i+1}')
                print(f"  Processing gap {gap_id}: {description}")
                print(f"  Would log: form_processing_locator_gap, selector_button_render")
                
        else:
            print("EXECUTION PATH: Interactive selector DISABLED")
            print("Expected logs: form_interactive_selector_disabled")
            
            # Determine specific reason
            if not locator_gaps:
                print("  Reason: No locator gaps")
                print("  Would log: selector_not_needed")
            elif not website_url:
                print("  Reason: No website URL")
                print("  Would log: selector_unavailable_no_url")
            elif not website_url.startswith(('http://', 'https://')):
                print("  Reason: Invalid website URL")
                print("  Would log: selector_unavailable_invalid_url")
            else:
                print("  Reason: Unknown")
                print("  Would log: selector_unavailable_unknown")
        
        print(f"Expected logs for this case: {case['expected_logs']}")

def check_code_structure():
    """Check the actual code structure in gap_analysis.py."""
    
    print("\n=== Checking Code Structure ===")
    
    try:
        gap_analysis_file = Path("core/gap_analysis.py")
        if not gap_analysis_file.exists():
            print("❌ gap_analysis.py not found")
            return
            
        with open(gap_analysis_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Look for key log statements
        key_logs = [
            "form_interactive_selector_disabled",
            "selector_button_render", 
            "selector_flags_set",
            "form_interactive_selector_enabled",
            "selector_unavailable_no_url"
        ]
        
        print("Searching for key log statements in gap_analysis.py:")
        
        for log_name in key_logs:
            found_lines = []
            for i, line in enumerate(lines, 1):
                if log_name in line and 'log_gap_analysis' in line:
                    found_lines.append(i)
            
            if found_lines:
                print(f"✅ {log_name}: Found on lines {found_lines}")
            else:
                print(f"❌ {log_name}: NOT FOUND")
        
        # Check for the main conditional structure
        print("\nChecking main conditional structure:")
        
        main_condition_line = None
        for i, line in enumerate(lines, 1):
            if "if main_condition:" in line or "if locator_gaps and website_url and website_url.startswith" in line:
                main_condition_line = i
                break
        
        if main_condition_line:
            print(f"✅ Main condition found on line {main_condition_line}")
            
            # Show context around the main condition
            start = max(0, main_condition_line - 5)
            end = min(len(lines), main_condition_line + 10)
            
            print(f"\nContext around line {main_condition_line}:")
            for i in range(start, end):
                marker = ">>> " if i == main_condition_line - 1 else "    "
                print(f"{marker}{i+1:3d}: {lines[i].rstrip()}")
        else:
            print("❌ Main condition structure not found")
            
    except Exception as e:
        print(f"❌ Error reading gap_analysis.py: {e}")

def check_function_calls():
    """Check if display_gap_filling_form is being called correctly."""
    
    print("\n=== Checking Function Call Patterns ===")
    
    # Check stage10.py for how display_gap_filling_form is called
    try:
        stage10_file = Path("stages/stage10.py")
        if stage10_file.exists():
            with open(stage10_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "display_gap_filling_form" in content:
                print("✅ display_gap_filling_form is called in stage10.py")
                
                # Find the call
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if "display_gap_filling_form" in line:
                        print(f"  Line {i}: {line.strip()}")
                        
                        # Show context
                        start = max(0, i - 3)
                        end = min(len(lines), i + 3)
                        print(f"  Context:")
                        for j in range(start, end):
                            marker = "  >>> " if j == i - 1 else "      "
                            print(f"{marker}{j+1:3d}: {lines[j]}")
                        print()
            else:
                print("❌ display_gap_filling_form is NOT called in stage10.py")
        else:
            print("❌ stage10.py not found")
            
    except Exception as e:
        print(f"❌ Error reading stage10.py: {e}")

if __name__ == "__main__":
    print("Gap Form Logic Analysis")
    print("=" * 50)
    
    # Analyze the conditional logic
    analyze_gap_form_logic()
    
    # Check the actual code structure
    check_code_structure()
    
    # Check function call patterns
    check_function_calls()
    
    print("\n" + "=" * 50)
    print("Analysis complete.")
    print("\nKey findings:")
    print("1. Check if the main condition logic is working as expected")
    print("2. Verify that log statements exist in the code")
    print("3. Confirm that display_gap_filling_form is being called")
    print("4. Look for any early returns or exceptions that skip the logging")
